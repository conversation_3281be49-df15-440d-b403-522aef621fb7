# -*- mode: python ; coding: utf-8 -*-
# YF AI Chat Backend v2 - 高性能版本打包配置
# 包含Windows效能模式优化和连接错误修复

from PyInstaller.utils.hooks import collect_all
import os

# 数据文件 - 包含所有必要的配置和资源文件
datas = [
    ('.env.example', '.'),
    ('config', 'config'),
    ('salesgpt_config', 'salesgpt_config'),
    ('blacklist_cache', 'blacklist_cache'),
    ('history', 'history'),
    ('logs', 'logs'),
    ('ssl', 'ssl'),
    ('utils', 'utils'),
    ('services', 'services'),
    ('models', 'models'),
    # v2新增文件
    ('power_management.py', '.'),
    ('exe_performance_wrapper.py', '.'),
    ('logging_config.py', '.'),
    ('start_high_performance.py', '.'),
    ('Windows高性能模式使用说明.md', '.'),
    ('连接错误修复说明.md', '.'),
]

binaries = []

# 隐藏导入 - 包含所有必要的模块
hiddenimports = [
    # 核心框架
    'fastapi',
    'uvicorn',
    'uvicorn.lifespan.on',
    'pydantic',
    'sqlalchemy',
    'aiosqlite',
    'httpx',
    
    # AI和LLM
    'google.generativeai',
    'openai',
    'langchain',
    'langchain_google_genai',
    'langchain_openai',
    'tiktoken',
    
    # 网络和通信
    'websockets',
    'python_multipart',
    
    # 工具库
    'pytz',
    'psutil',
    
    # 系统API
    'ctypes',
    'ctypes.wintypes',
    
    # 项目模块
    'services.database',
    'services.salesgpt_service',
    'services.protection_service',
    'services.ip_blacklist_service',
    'services.memory_service',
    'services.form_service',
    'services.customer_service',
    'services.conversation_rules_service',
    'services.escalation_service',
    'services.customer_segmentation_service',
    'models.chat_models',
    'utils.file_logger',
    'utils.timezone_config',
    'utils.path_utils',
    
    # v2新增模块
    'power_management',
    'exe_performance_wrapper',
    'logging_config',
]

# 收集所有依赖包
packages_to_collect = [
    'pydantic',
    'pydantic_core',
    'aiosqlite',
    'sqlalchemy',
    'langchain',
    'langchain_core',
    'langchain_google_genai',
    'langchain_openai',
    'openai',
    'tiktoken',
    'psutil',
]

for package in packages_to_collect:
    try:
        tmp_ret = collect_all(package)
        datas += tmp_ret[0]
        binaries += tmp_ret[1]
        hiddenimports += tmp_ret[2]
    except Exception as e:
        print(f"Warning: Could not collect {package}: {e}")

# 分析配置
a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=['.'],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # 排除不需要的模块以减小文件大小
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'tkinter',
    ],
    noarchive=False,
    optimize=2,  # 启用优化
)

# PYZ配置
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# EXE配置 - v2高性能版本
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='YF_AI_Chat_Backend_v2',  # v2版本名称
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # 启用UPX压缩
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['resources\\text_icon.ico'] if os.path.exists('resources\\text_icon.ico') else None,
    version_info={
        'version': (2, 0, 0, 0),
        'description': 'YF AI Chat Backend v2 - High Performance Edition',
        'product_name': 'YF AI Chat Backend v2',
        'product_version': '2.0.0',
        'file_version': '2.0.0',
        'company_name': 'YF Company',
        'copyright': 'Copyright (C) 2024 YF Company',
    }
)
