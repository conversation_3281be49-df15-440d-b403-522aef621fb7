import os
import sys
import asyncio
import logging
from fastapi import FastAPI, HTTPException, Depends, WebSocket, WebSocketDisconnect, BackgroundTasks, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from contextlib import asynccontextmanager
import json
from datetime import datetime
import uvicorn
from dotenv import load_dotenv

# 导入日志配置
from logging_config import setup_logging_filters, configure_uvicorn_logging

# 导入电源管理
from power_management import start_power_protection, stop_power_protection, get_power_status

# 修复Google Generative AI在PyInstaller环境中的兼容性问题
def fix_google_genai_compatibility():
    """修复Google Generative AI在PyInstaller环境中的兼容性问题"""
    try:
        # 检查是否在PyInstaller环境中
        if getattr(sys, 'frozen', False):
            # 在PyInstaller环境中，设置必要的环境变量
            os.environ.setdefault('GOOGLE_GENAI_USE_VERTEXAI', 'false')
            
            # 修复string_utils中的splitlines问题
            import google.generativeai.string_utils as string_utils
            original_strip_oneof = string_utils.strip_oneof
            
            def patched_strip_oneof(text, *args, **kwargs):
                if text is None:
                    return ""
                return original_strip_oneof(text, *args, **kwargs)
            
            string_utils.strip_oneof = patched_strip_oneof
            
    except Exception as e:
        print(f"⚠️ Google Generative AI兼容性修复失败: {e}")

# 应用兼容性修复
fix_google_genai_compatibility()

# 导入EXE性能包装器
try:
    from exe_performance_wrapper import setup_performance_optimization, keep_system_awake
    EXE_PERFORMANCE_AVAILABLE = True
except ImportError:
    EXE_PERFORMANCE_AVAILABLE = False

from services.database import DatabaseService
from services.salesgpt_service import SalesGPTService
from services.protection_service import ProtectionService
from services.ip_blacklist_service import IPBlacklistService
from services.memory_service import MemoryService
from services.form_service import FormService
from services.customer_service import CustomerService
from services.escalation_service import EscalationService
from services.conversation_rules_service import ConversationRulesService
from services.customer_segmentation_service import CustomerSegmentationService
from models.chat_models import ChatMessage, ChatResponse, AdminMessage, FormSubmission
from utils.file_logger import FileLogger
from utils.timezone_config import get_timezone_config

# 设置日志
logger = FileLogger()

# 全局服务实例
db_service = None
salesgpt_service = None
protection_service = None
ip_blacklist_service = None
memory_service = None
form_service = None
customer_service = None
escalation_service = None
conversation_rules_service = None
customer_segmentation_service = None

# WebSocket连接管理器
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.admin_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket, is_admin: bool = False):
        await websocket.accept()
        if is_admin:
            self.admin_connections.append(websocket)
            logger.websocket_event("管理员连接", "admin", f"当前管理员连接数: {len(self.admin_connections)}")
        else:
            self.active_connections.append(websocket)
            logger.websocket_event("用户连接", "user", f"当前用户连接数: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket, is_admin: bool = False):
        if is_admin and websocket in self.admin_connections:
            self.admin_connections.remove(websocket)
            logger.websocket_event("管理员断开", "admin", f"当前管理员连接数: {len(self.admin_connections)}")
        elif websocket in self.active_connections:
            self.active_connections.remove(websocket)
            logger.websocket_event("用户断开", "user", f"当前用户连接数: {len(self.active_connections)}")

    async def broadcast_to_admins(self, message: str):
        """向所有管理员广播消息"""
        for connection in self.admin_connections:
            try:
                await connection.send_text(message)
            except:
                self.admin_connections.remove(connection)

manager = ConnectionManager()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.startup("🚀 YF AI Chat Backend 启动中...")

    # 检查是否为打包的EXE环境
    is_exe = getattr(sys, 'frozen', False)
    if is_exe and EXE_PERFORMANCE_AVAILABLE:
        logger.startup("📦 检测到EXE环境，应用高性能优化...")
        setup_performance_optimization()
        keep_system_awake()
        logger.success("✅ EXE高性能优化已启用")

    # 初始化服务
    await initialize_services()
    
    # 启动电源保护
    start_power_protection()
    logger.startup("🛡️ 电源保护已启动")
    
    logger.success("✅ YF AI Chat Backend 启动完成")
    
    yield
    
    # 关闭时执行
    logger.shutdown("🔄 YF AI Chat Backend 关闭中...")
    
    # 停止电源保护
    stop_power_protection()
    logger.shutdown("🛡️ 电源保护已停止")
    
    # 清理服务
    await cleanup_services()
    
    logger.shutdown("👋 YF AI Chat Backend 已关闭")

async def initialize_services():
    """初始化所有服务"""
    global db_service, salesgpt_service, protection_service, ip_blacklist_service
    global memory_service, form_service, customer_service, escalation_service
    global conversation_rules_service, customer_segmentation_service
    
    try:
        # 初始化数据库服务
        db_service = DatabaseService()
        await db_service.initialize()
        logger.startup("✅ 数据库服务初始化完成")
        
        # 初始化其他服务
        salesgpt_service = SalesGPTService()
        protection_service = ProtectionService()
        ip_blacklist_service = IPBlacklistService()
        memory_service = MemoryService(db_service)
        form_service = FormService(db_service)
        customer_service = CustomerService(db_service)
        escalation_service = EscalationService(db_service)
        conversation_rules_service = ConversationRulesService(db_service)
        customer_segmentation_service = CustomerSegmentationService(db_service)
        
        logger.startup("✅ 所有服务初始化完成")
        
    except Exception as e:
        logger.error(f"❌ 服务初始化失败: {e}")
        raise

async def cleanup_services():
    """清理所有服务"""
    global db_service
    
    try:
        if db_service:
            await db_service.close()
            logger.shutdown("✅ 数据库连接已关闭")
    except Exception as e:
        logger.error(f"⚠️ 服务清理时出错: {e}")

# 创建FastAPI应用
app = FastAPI(
    title="YF AI Chat Backend",
    description="YF AI Chat Backend API",
    version="2.0.0",
    lifespan=lifespan
)

# CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 安全认证
security = HTTPBearer()

# 认证依赖
def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """验证访问令牌"""
    token = credentials.credentials
    
    # 检查WordPress令牌
    wordpress_token = os.getenv("WORDPRESS_TOKEN")
    if wordpress_token and token == wordpress_token:
        return {"type": "wordpress", "token": token}
    
    # 检查管理员令牌
    admin_token = os.getenv("ADMIN_TOKEN")
    if admin_token and token == admin_token:
        return {"type": "admin", "token": token}
    
    raise HTTPException(status_code=401, detail="无效的访问令牌")

def verify_admin_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """验证管理员令牌"""
    token = credentials.credentials
    admin_token = os.getenv("ADMIN_TOKEN")
    
    if not admin_token or token != admin_token:
        raise HTTPException(status_code=401, detail="需要管理员权限")
    
    return {"type": "admin", "token": token}

# 基础路由
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "YF AI Chat Backend v2.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat(),
        "features": [
            "高性能模式优化",
            "智能连接错误处理", 
            "增强的性能监控",
            "WordPress插件集成"
        ]
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        # 检查数据库连接
        db_status = "connected" if db_service and await db_service.health_check() else "disconnected"
        
        # 检查电源状态
        power_status = get_power_status()
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "database": db_status,
            "power_protection": power_status.get("power_protection_active", False),
            "current_power_plan": power_status.get("current_power_plan", "unknown"),
            "connections": {
                "users": len(manager.active_connections),
                "admins": len(manager.admin_connections)
            }
        }
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

# 聊天API路由
@app.post("/api/chat", response_model=ChatResponse)
async def chat_endpoint(message: ChatMessage, auth: dict = Depends(verify_token)):
    """聊天API端点"""
    try:
        logger.api_request("聊天请求", message.session_id, f"消息: {message.message[:50]}...")

        # 保存用户消息
        user_msg = await db_service.save_message(
            session_id=message.session_id,
            message=message.message,
            sender="user",
            metadata=message.metadata
        )

        # 向管理员广播新消息通知
        await manager.broadcast_to_admins(json.dumps({
            "type": "new_message",
            "session_id": message.session_id,
            "message": user_msg.model_dump()
        }, default=str))
        logger.websocket_event("新消息通知", message.session_id, f"{len(manager.admin_connections)} 个管理员连接")

        # 检查是否有管理员接管
        if escalation_service.is_admin_controlled(message.session_id):
            logger.chat_event("管理员接管", message.session_id, "等待管理员回复")
            return {
                "response": "您的消息已收到，客服人员正在为您处理，请稍候...",
                "session_id": message.session_id,
                "admin_controlled": True,
                "user_message_id": user_msg.id,
                "user_message_data": user_msg.model_dump()
            }

        # 使用SalesGPT处理消息
        try:
            salesgpt_response = await salesgpt_service.process_message(
                message.message,
                message.session_id,
                message.metadata
            )

            # 保存AI回复
            ai_msg = await db_service.save_message(
                session_id=message.session_id,
                message=salesgpt_response.get('response', '抱歉，我现在无法回复您的消息。'),
                sender="assistant",
                metadata=salesgpt_response.get('metadata', {})
            )

            logger.chat_event("SalesGPT回复", message.session_id, f"回复长度: {len(salesgpt_response.get('response', ''))}")

            # 检查是否需要人工接管
            if salesgpt_response.get('handover_requested', False):
                escalation_service.request_handover(message.session_id, "AI请求人工接管")
                logger.escalation_event("AI请求接管", message.session_id, "复杂问题需要人工处理")

                return {
                    "response": salesgpt_response.get('response'),
                    "session_id": message.session_id,
                    "user_message_id": user_msg.id,
                    "user_message_data": user_msg.model_dump(),
                    "handover_requested": True,
                    "status": "handover"
                }

            return {
                "response": salesgpt_response.get('response'),
                "session_id": message.session_id,
                "user_message_id": user_msg.id,
                "user_message_data": user_msg.model_dump(),
                "ai_message_id": ai_msg.id,
                "ai_message_data": ai_msg.model_dump(),
                "metadata": salesgpt_response.get('metadata', {})
            }

        except Exception as e:
            logger.error(f"SalesGPT处理失败: {e}")

            # 保存错误回复
            error_response = "抱歉，我现在遇到了一些技术问题，请稍后再试或联系客服人员。"
            ai_msg = await db_service.save_message(
                session_id=message.session_id,
                message=error_response,
                sender="assistant",
                metadata={"error": str(e)}
            )

            return {
                "response": error_response,
                "session_id": message.session_id,
                "ai_message_id": ai_msg.id,
                "ai_message_data": ai_msg.model_dump(),
                "user_message_id": user_msg.id,
                "user_message_data": user_msg.model_dump(),
                "error_fallback": True
            }

    except Exception as e:
        logger.error(f"聊天API错误: {e}")
        raise HTTPException(status_code=500, detail=f"聊天处理失败: {str(e)}")

# 管理员API路由
@app.post("/api/admin/reply")
async def admin_reply(message: AdminMessage, auth: dict = Depends(verify_admin_token)):
    """管理员回复API"""
    try:
        logger.admin_action("管理员回复", message.session_id, f"回复: {message.message[:50]}...")

        # 保存管理员消息
        admin_msg = await db_service.save_message(
            session_id=message.session_id,
            message=message.message,
            sender="admin",
            metadata={"admin_id": message.admin_id}
        )

        # 设置会话为管理员控制
        escalation_service.set_admin_control(message.session_id, message.admin_id)

        return {
            "success": True,
            "message_id": admin_msg.id,
            "session_id": message.session_id,
            "timestamp": admin_msg.timestamp
        }

    except Exception as e:
        logger.error(f"管理员回复错误: {e}")
        raise HTTPException(status_code=500, detail=f"管理员回复失败: {str(e)}")

@app.get("/api/admin/sessions")
async def get_admin_sessions(auth: dict = Depends(verify_admin_token)):
    """获取所有会话列表"""
    try:
        sessions = await db_service.get_all_sessions()
        return {
            "sessions": sessions,
            "total": len(sessions),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取会话列表错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取会话列表失败: {str(e)}")

@app.get("/api/admin/session/{session_id}/messages")
async def get_session_messages(session_id: str, auth: dict = Depends(verify_admin_token)):
    """获取指定会话的消息历史"""
    try:
        messages = await db_service.get_session_messages(session_id)
        return {
            "session_id": session_id,
            "messages": messages,
            "total": len(messages),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取会话消息错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取会话消息失败: {str(e)}")

# WebSocket路由
@app.websocket("/ws/admin")
async def websocket_admin_endpoint(websocket: WebSocket):
    """管理员WebSocket端点"""
    await manager.connect(websocket, is_admin=True)
    try:
        while True:
            data = await websocket.receive_text()
            # 处理管理员WebSocket消息
            logger.websocket_event("管理员消息", "admin", f"数据: {data[:50]}...")
    except WebSocketDisconnect:
        manager.disconnect(websocket, is_admin=True)

@app.websocket("/ws/user/{session_id}")
async def websocket_user_endpoint(websocket: WebSocket, session_id: str):
    """用户WebSocket端点"""
    await manager.connect(websocket, is_admin=False)
    try:
        while True:
            data = await websocket.receive_text()
            # 处理用户WebSocket消息
            logger.websocket_event("用户消息", session_id, f"数据: {data[:50]}...")
    except WebSocketDisconnect:
        manager.disconnect(websocket, is_admin=False)

if __name__ == "__main__":
    # 加载环境变量
    load_dotenv()

    # 设置日志过滤器
    setup_logging_filters()

    # 获取配置
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8000))

    # 运行应用
    uvicorn.run(
        app,
        host=host,
        port=port,
        log_config=configure_uvicorn_logging()
    )
